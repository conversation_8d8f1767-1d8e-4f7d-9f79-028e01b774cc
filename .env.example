# --- AI 模型相关配置 ---
# 模型的API Key。
OPENAI_API_KEY="sk-..."

# 模型的API接口地址。这里需要填写服务商提供的、兼容OpenAI格式的API地址，基本所有模型都有提供OpenAI格式兼容的接口
# 可查阅你使用的大模型API文档，如格式为 https://xx.xx.com/v1/chat/completions 则OPENAI_BASE_URL只需要填入前半段 https://xx.xx.com/v1/
OPENAI_BASE_URL="https://generativelanguage.googleapis.com/v1beta/openai/"

# 使用的模型名称，模型需要支持图片上传。
OPENAI_MODEL_NAME="gemini-2.5-pro"

# (可选) 为AI请求配置HTTP/S代理。支持 http 和 socks5。例如: http://127.0.0.1:7890 或 socks5://127.0.0.1:1080
PROXY_URL=""

# ntfy 通知服务配置
NTFY_TOPIC_URL="https://ntfy.sh/your-topic-name" # 替换为你的 ntfy 主题 URL

# (可选) Gotify 通知服务配置
GOTIFY_URL="" # 你的 Gotify 服务地址, 例如: https://push.example.de
GOTIFY_TOKEN="" # 你的 Gotify 应用的 Token

# (可选) Bark 通知服务配置
BARK_URL="" # 你的 Bark 推送地址, 例如: https://api.day.app/your_key

# 企业微信机器人通知配置 如果无则不用配置
WX_BOT_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxx"

# (可选) 通用 Webhook 通知配置
WEBHOOK_URL="" # 你的 Webhook URL, 例如: https://foo.bar.com/quz?a=b
WEBHOOK_METHOD="POST" # 请求方法: "GET" 或 "POST"
WEBHOOK_HEADERS='{"X-API-TOKEN":"your-secret-token"}' # 自定义请求头 (JSON格式)
WEBHOOK_CONTENT_TYPE="JSON" # POST请求内容类型: "JSON" 或 "FORM"
WEBHOOK_QUERY_PARAMETERS='{"title":"{{title}}","content":"{{content}}"}' # GET请求的查询参数 (JSON格式, 支持 {{title}}, {{content}} 占位符)
WEBHOOK_BODY='{"title":"{{title}}","content":"{{content}}"}' # POST请求的请求体 (JSON格式, 支持 {{title}}, {{content}} 占位符)

# 是否使用edge浏览器 默认使用chrome浏览器
LOGIN_IS_EDGE=false

# 是否开启电脑链接转换为手机链接
PCURL_TO_MOBILE=true

# 爬虫是否以无头模式运行 (true/false)。
# 本地运行时遇到滑动验证码时，可设为 false 手动进行滑动验证，如果出现风控建议停止运行。
# 使用docker部署不支持GUI，设置 RUN_HEADLESS=true 否则无法运行。
RUN_HEADLESS=true

# (可选) AI调试模式 (true/false)。开启后会在控制台打印更多用于排查AI分析问题的日志。
AI_DEBUG_MODE=false

# 服务端口自定义 不配置默认8000
SERVER_PORT=8000
