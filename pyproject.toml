[project]
name = "ai-goofish-monitor"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "apscheduler>=3.11.0",
    "dotenv>=0.9.9",
    "fastapi>=0.116.1",
    "httpx[socks]>=0.28.1",
    "jinja2>=3.1.6",
    "openai>=1.98.0",
    "pillow>=11.3.0",
    "playwright>=1.54.0",
    "python-dotenv>=1.1.1",
    "python-socks>=2.7.2",
    "pyzbar>=0.1.9",
    "qrcode>=8.2",
    "requests>=2.32.4",
    "uvicorn[standard]>=0.35.0",
]
